import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerService } from './util/swagger/swagger.service';
import { isDev } from './util/utilFunction/utilFunction';
import { ResponseTransformInterceptor } from './web/http_response_result/response-transform.interceptor';
import { LoggerService } from './common/logger/logger.service';
import { GlobalExceptionFilter } from './common/logger/global-exception.filter';
// DDD架构：导入统一异常过滤器
import { UnifiedExceptionFilter } from './web/http_exception_filter/unified-exception.filter';
import { HttpResponseResultService } from './web/http_response_result/http_response_result.service';
import { RequestTraceService } from './web/http_response_result/request-trace.service';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import * as express from 'express';


async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    abortOnError: false,
    bufferLogs: true
  });

  // 获取自定义日志服务
  const loggerService = app.get(LoggerService);
  const httpResponseService = app.get(HttpResponseResultService);

  // 注意：RequestTraceService是请求级作用域，不能在这里直接获取
  // 它会在UnifiedExceptionFilter中通过依赖注入自动获取

  // 使用自定义日志服务
  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));

  // DDD架构：设置统一异常过滤器（替换GlobalExceptionFilter）
  // RequestTraceService会通过依赖注入自动提供给UnifiedExceptionFilter
  app.useGlobalFilters(new UnifiedExceptionFilter(httpResponseService, loggerService));

  // 备用：如果需要保留原有的GlobalExceptionFilter，可以同时使用
  // app.useGlobalFilters(new GlobalExceptionFilter(loggerService));

  // 记录应用启动日志
  loggerService.logStartup('Application is starting...', {
    nodeVersion: process.version,
    platform: process.platform,
    environment: process.env.NODE_ENV || 'development',
    port: process.env.PORT || 8003
  });

  // 新增请求体大小限制
  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ limit: '50mb', extended: true }));

  // 添加用于解析微信XML消息的原始body处理中间件
  app.use((req, res, next) => {
    if (req.url.includes('/weixin/message') && req.method === 'POST') {
      let data = '';
      req.setEncoding('utf8');
      req.on('data', (chunk) => {
        data += chunk;
      });
      req.on('end', () => {
        req.body = data;
        loggerService.log(`[微信消息原始数据]: ${data}`, 'WeChat');
        next();
      });
    } else {
      next();
    }
  });

  // 禁用微信URL的正文解析，因为我们需要使用原始字符串
  app.use((req, res, next) => {
    if (req.url.includes('/weixin/message') && req.method === 'GET') {
      // 对微信URL验证请求特殊处理
      loggerService.log(`[微信URL验证]: ${req.url}`, 'WeChat');
    }
    next();
  });

  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));


  // 注册全局响应转换拦截器
  app.useGlobalInterceptors(new ResponseTransformInterceptor());

  // 配置Swagger
  if (isDev()) {
    const swaggerService = new SwaggerService();
    swaggerService.setup(app);
  }

  // 启用CORS
  app.enableCors();

  // 设置全局路径前缀 (可选)
  // app.setGlobalPrefix('api');

  const port = process.env.PORT ?? 8003;
  await app.listen(port);

  const url = await app.getUrl();

  // 记录启动成功日志
  loggerService.logStartup('Application started successfully', {
    url,
    port,
    timestamp: new Date().toISOString()
  });
}

bootstrap().catch((error) => {
  // 这里仍然使用原始console.error，因为此时日志系统可能还未初始化
  console.error('应用启动失败:', error);
  process.exit(1);
});
