import { HttpException } from '@nestjs/common';
import { ErrorType } from './unified-response.interface';
export declare class UnifiedBusinessException extends HttpException {
    readonly errorType: ErrorType;
    readonly errorDetails?: any;
    readonly businessCode: number;
    constructor(businessCode: number, message?: string, data?: any, details?: any);
    private static getDefaultMessage;
    static userNotFound(userId?: number | string, details?: any): UnifiedBusinessException;
    static userAlreadyExists(identifier?: string, details?: any): UnifiedBusinessException;
    static tokenExpired(details?: any): UnifiedBusinessException;
    static permissionDenied(resource?: string, action?: string, details?: any): UnifiedBusinessException;
    static insufficientBalance(currentBalance?: number, requiredAmount?: number, details?: any): UnifiedBusinessException;
    static duplicateOperation(operation?: string, details?: any): UnifiedBusinessException;
    static resourceLocked(resourceType?: string, resourceId?: string, details?: any): UnifiedBusinessException;
    static systemMaintenance(estimatedTime?: string, details?: any): UnifiedBusinessException;
    static rateLimitExceeded(retryAfter?: number, details?: any): UnifiedBusinessException;
    static externalServiceError(serviceName?: string, details?: any): UnifiedBusinessException;
}
export declare class BusinessException extends UnifiedBusinessException {
}
export declare class UserNotFoundException extends UnifiedBusinessException {
    constructor(userId?: number | string, details?: any);
}
export declare class PermissionDeniedException extends UnifiedBusinessException {
    constructor(resource?: string, action?: string, details?: any);
}
export declare class TokenExpiredException extends UnifiedBusinessException {
    constructor(details?: any);
}
export declare class InsufficientBalanceException extends UnifiedBusinessException {
    constructor(currentBalance?: number, requiredAmount?: number, details?: any);
}
export declare class DuplicateOperationException extends UnifiedBusinessException {
    constructor(operation?: string, details?: any);
}
export declare class ResourceLockedException extends UnifiedBusinessException {
    constructor(resourceType?: string, resourceId?: string, details?: any);
}
