import { UserPointService } from './user_point.service';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
import { UserPointsService } from 'src/util/database/mysql/user_points/user_points.service';
import { UserClassService } from '../../web/user_class/user_class.service';
import { UserInfoService } from '../../util/database/mysql/user_info/user_info.service';
export declare class UserPointController {
    private readonly userPointsService;
    private readonly httpResponseResultService;
    private readonly userPointService;
    private readonly userClassService;
    private readonly userInfoService;
    constructor(userPointsService: UserPointsService, httpResponseResultService: HttpResponseResultService, userPointService: UserPointService, userClassService: UserClassService, userInfoService: UserInfoService);
    private calculateTotalPoints;
    getTotal(request: any): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<{
        total: number;
    }> | import("../../common/exceptions/unified-response.interface").HttpResponse<null>>;
    getUserPoints(userId: number, request: any): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<{
        total: number;
    }>>;
    getDetails(request: any): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<import("../../util/database/mysql/user_points/entities/user_point.entity").UserPoint[]>>;
    addPoints(request: any, points: number, source: number, remark?: string): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<import("../../util/database/mysql/user_points/entities/user_point.entity").UserPoint>>;
    usePoints(request: any, points: number, usage: number, remark?: string): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<{
        deductedPoints: number;
        remainingPoints: number;
    }>>;
    checkPoints(request: any, serviceType: string, requiredPoints: number): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<{
        sufficient: boolean;
        currentPoints: number;
        requiredPoints: number;
        message: string | undefined;
    }>>;
    assignPoints(request: any, body: {
        userId: number;
        points: number;
    }): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<import("../../util/database/mysql/user_points/entities/user_point.entity").UserPoint>>;
    getBatchUserPoints(userIds: number[], request: any): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<{
        [userId: number]: {
            totalPoints: number;
            availablePoints: number;
        };
    }>>;
    getStudentPointsDetail(request: any): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<{
        totalPoints: number;
        teacherPoints: {
            total: number;
            details: Array<{
                permissionId: number;
                teacherId: number;
                availablePoints: number;
                expireTime: Date | null;
                createTime: Date;
                remark: string;
            }>;
        };
        packagePoints: {
            total: number;
            nonSpecial: number;
            special: number;
            other: number;
            packages: Array<{
                id: number;
                packageId: number;
                packageName?: string;
                points: number;
                assignType: number;
                assignTypeName: string;
                expireTime: Date;
                status: number;
            }>;
        };
    }>>;
    checkStudentPoints(request: any, requiredPoints: number): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<{
        sufficient: boolean;
        total: number;
        fromTeacher: number;
        fromNonSpecialPackage: number;
        requiredPoints: number;
        deficit: number;
    }>>;
    teacherGetStudentDetail(studentId: number, request: any): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<{
        totalPoints: number;
        teacherPoints: {
            total: number;
            details: Array<{
                permissionId: number;
                teacherId: number;
                availablePoints: number;
                expireTime: Date | null;
                createTime: Date;
                remark: string;
            }>;
        };
        packagePoints: {
            total: number;
            nonSpecial: number;
            special: number;
            other: number;
            packages: Array<{
                id: number;
                packageId: number;
                packageName?: string;
                points: number;
                assignType: number;
                assignTypeName: string;
                expireTime: Date;
                status: number;
            }>;
        };
    }>>;
    getPackagesByRole(request: any): Promise<import("../../common/exceptions/unified-response.interface").HttpResponse<null> | import("../../common/exceptions/unified-response.interface").HttpResponse<any[]>>;
}
